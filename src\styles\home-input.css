/* 首页输入框样式 */
.input-cover {
  position: relative;
  display: flex;
  max-width: 100%;
  border: 1px solid var(--doc-blue-blue50, #dde8ff);
  border-radius: 16px;
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease-in-out;
}

.input-cover:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.agent-mode-cover {
  background: var(--input-field-bg-default, #f9fafc);
  z-index: 10;
  display: flex;
  justify-content: center;
  position: absolute;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.agent-mode-cover .agent-text {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.agent-mode-cover .line {
  width: 1px;
  height: 16px;
  background-color: #d1d5db;
  margin: 0 8px;
}

.agent-mode-cover .doc-text {
  color: var(--doc-blue, #4D5EFF);
  font-size: 14px;
  font-weight: 500;
  height: 28px;
  display: flex;
  align-items: center;
}

.el-textarea {
  position: relative;
  width: 100%;
}

.el-textarea__inner {
  width: 100%;
  border: 0;
  resize: none;
  outline: none;
  background: transparent;
  color: #1f2937;
  font-size: 16px;
  line-height: 1.5;
  transition: 0.02s ease-in-out;
  padding: 6px 12px 0px;
  height: 150px;
  min-height: 78px;
  text-indent: 151px;
}

.el-textarea__inner::placeholder {
  color: #9ca3af;
}

.el-textarea__inner:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-textarea__inner {
    text-indent: 120px;
    font-size: 14px;
    height: 120px;
    min-height: 60px;
  }
  
  .agent-mode-cover {
    padding: 3px 8px;
  }
  
  .agent-mode-cover .agent-text,
  .agent-mode-cover .doc-text {
    font-size: 12px;
  }
  
  .agent-mode-cover .line {
    height: 14px;
    margin: 0 6px;
  }
}

/* 加载动画 */
.loading-animation {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 模糊背景效果 */
.blur_container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, transparent, var(--doc-blue-blue100, #4d5eff));
  border-radius: 0 0 16px 16px;
  opacity: 0.1;
}
