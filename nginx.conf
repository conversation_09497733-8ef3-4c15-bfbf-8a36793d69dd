server {
  listen       3000;
  server_name  localhost;

  client_max_body_size     256m;
  client_header_buffer_size 10240k;
  large_client_header_buffers 6 10240k;
  port_in_redirect off;
###设置反向代理 超时的时长+++++++++++++++
    proxy_connect_timeout 90s;      
    proxy_read_timeout 90s;        
    proxy_send_timeout 90s; 
    

	# 文章快照访问
    location ~ ^/article/(.+)/(.+)\$ {
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_pass http://ai-base-server.${NAMESPACES}.svc.cluster.local:8080/ai-base/index/snapshot/article/\$2;
    }
    location /apps {
        rewrite ^/apps(.*)$ /$1 permanent;
    }
    # 原 ai-apps跳转到 apps
    location ~ ^/ai-apps/?(.*)$ {
      rewrite ^/ai-apps/?(.*)$ /$1 permanent;
    }

	# 原地址重定向到新地址
	location /apps/chat/ {
        rewrite /apps/chat/(.*)\$ /chat/\$1 permanent;
    }
    location /apps/tool/ {
        rewrite /apps/tool/(.*)\$ /tool/\$1 permanent;
    }
    
     # 固定路径匹配 - 英文版 novax-base (高优先级前缀匹配)
    # 支持: /en/novax-base, /en/novax-base/, /en/novax-base/new, /en/novax-base/{uuid}
    location ^~ /en/novax-base {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 固定路径匹配 - 中文版 novax-base (高优先级前缀匹配)
    # 支持: /zh/novax-base, /zh/novax-base/, /zh/novax-base/new, /zh/novax-base/{uuid}
    location ^~ /zh/novax-base {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 固定路径匹配 - 英文版 elavax-base (高优先级前缀匹配)
    # 支持: /en/elavax-base, /en/elavax-base/, /en/elavax-base/new, /en/elavax-base/{uuid}
    location ^~ /en/elavax-base {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 固定路径匹配 - 中文版 elavax-base (高优先级前缀匹配)
    # 支持: /zh/elavax-base, /zh/elavax-base/, /zh/elavax-base/new, /zh/elavax-base/{uuid}
    location ^~ /zh/elavax-base {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # ==================== 特定应用的精确重定向规则 ====================
    # 只有这三个特定应用会被重定向到本React项目，其他路由保持默认行为

    # novax-base 应用重定向（使用前缀匹配避免正则表达式问题）
    location = /novax-base {
        return 301 \$scheme://\$host/zh/novax-base;
    }
    location = /novax-base/ {
        return 301 \$scheme://\$host/zh/novax-base/;
    }
    # 使用前缀匹配处理带子路径的novax-base（包括会话ID和new）
    location ^~ /novax-base/ {
        return 301 \$scheme://\$host/zh\$request_uri;
    }

    # elavax-base 应用重定向（使用前缀匹配避免正则表达式问题）
    location = /elavax-base {
        return 301 \$scheme://\$host/zh/elavax-base;
    }
    location = /elavax-base/ {
        return 301 \$scheme://\$host/zh/elavax-base/;
    }
    # 使用前缀匹配处理带子路径的elavax-base（包括会话ID和new）
    location ^~ /elavax-base/ {
        return 301 \$scheme://\$host/zh\$request_uri;
    }

    # elavax-pro 应用重定向（使用前缀匹配避免正则表达式问题）
    location = /elavax-pro {
        return 301 \$scheme://\$host/zh/elavax-pro;
    }
    location = /elavax-pro/ {
        return 301 \$scheme://\$host/zh/elavax-pro/;
    }
    # 使用前缀匹配处理带子路径的elavax-pro（包括会话ID和new）
    location ^~ /elavax-pro/ {
        return 301 \$scheme://\$host/zh\$request_uri;
    }

    # novax-pro 应用重定向（使用前缀匹配避免正则表达式问题）
    location = /novax-pro {
        return 301 \$scheme://\$host/zh/novax-pro;
    }
    location = /novax-pro/ {
        return 301 \$scheme://\$host/zh/novax-pro/;
    }
    # 使用前缀匹配处理带子路径的novax-pro（包括会话ID和new）
    location ^~ /novax-pro/ {
        return 301 \$scheme://\$host/zh\$request_uri;
    }

    # ==================== 其他应用的固定路径配置 ====================
    # 如需添加新应用，请按以下模板添加对应的中英文版本

    # 固定路径匹配 - 英文版 elavax-pro (高优先级前缀匹配)
    # 支持: /en/elavax-pro, /en/elavax-pro/, /en/elavax-pro/new, /en/elavax-pro/{uuid}
    location ^~ /en/elavax-pro {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 固定路径匹配 - 中文版 elavax-pro (高优先级前缀匹配)
    # 支持: /zh/elavax-pro, /zh/elavax-pro/, /zh/elavax-pro/new, /zh/elavax-pro/{uuid}
    location ^~ /zh/elavax-pro {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 固定路径匹配 - 英文版 novax-pro (高优先级前缀匹配)
    # 支持: /en/novax-pro, /en/novax-pro/, /en/novax-pro/new, /en/novax-pro/{uuid}
    location ^~ /en/novax-pro {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 固定路径匹配 - 中文版 novax-pro (高优先级前缀匹配)
    # 支持: /zh/novax-pro, /zh/novax-pro/, /zh/novax-pro/new, /zh/novax-pro/{uuid}
    location ^~ /zh/novax-pro {
        alias /usr/share/nginx/html/apps/project;
        index index.html;

        # K8s环境代理头设置
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 流式接口优化
        proxy_buffering off;
        proxy_cache off;
        gzip off;
        add_header Cache-Control "no-cache";

        # SPA路由处理 - 支持子路径（new和UUID格式的会话ID）
        try_files \$uri \$uri/ /index.html;

        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }


	# 医生成长赋能智能体
    location /idoc {
        root /usr/share/nginx/html/apps;
        index index.html index.htm;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;     
        try_files \$uri \$uri/ /idoc;
    }
	# 医生成长赋能智能体
    location /elavax {
        root /usr/share/nginx/html/apps;
        index index.html index.htm;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;     
        try_files \$uri \$uri/ /elavax;
    }
    # 医生成长赋能智能体
    location /novax {
        root /usr/share/nginx/html/apps;
        index index.html index.htm;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;     
        try_files \$uri \$uri/ /novax;
    }
    # 医学写作，嵌套在write 的iframe里面
    location /ai-write {
        root /usr/share/nginx/html;
        index index.html index.htm;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;     
        try_files \$uri \$uri/ /ai-write/index.html;
    }
	
    # 医学会话，嵌套在chat 的iframe里面
    location /ai-chat {
        root /usr/share/nginx/html/apps;
        index index.html index.htm;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;     
        try_files \$uri \$uri/ /ai-chat/index.html;
    }
    # 一三项目会话
    location /daiichi-sankyo {
        root /usr/share/nginx/html/apps;
        index index.html index.htm;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Real-PORT \$remote_port;
        proxy_set_header REMOTE-HOST \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;     
        try_files \$uri \$uri/ /daiichi-sankyo/index.html;
    }
     # dify接口
    location /v1 {
  		proxy_http_version 1.1;
  		proxy_pass http://dify-api.${NAMESPACES}.svc.cluster.local:5001;
 	}
    # ai交互接口
    location /dev-api/ai-base/v1 {
        proxy_pass http://ai-base-server.${NAMESPACES}.svc.cluster.local:8080/ai-base/v1;
    
        proxy_http_version 1.1;
        #proxy_set_header Connection "";
    
        #proxy_buffering off;
        #proxy_cache off;
        #proxy_request_buffering off;
        #proxy_set_header X-Accel-Buffering no;
    
        #tcp_nopush off;
        #tcp_nodelay on;
    
        add_header 'Cache-Control' 'no-cache';
    
        proxy_connect_timeout 300s;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
    
        #gzip off;
    } 
	# 后端接口
    location /dev-api/ {
        proxy_http_version 1.1;
        proxy_pass http://ai-base-server.${NAMESPACES}.svc.cluster.local:8080/;
    }
	# pgraphql接口
    location /pgraphql-api/ {
        proxy_http_version 1.1;
        proxy_pass http://pgraphql-engine.${NAMESPACES}.svc.cluster.local:8080/;
    }
  	# Prompts工厂
  	location /pages/ {
     	alias /usr/share/nginx/html/apps/pages/;
     	try_files \$uri \$uri/ /index.html;
    }
	# 根跳转到首页
  	location / {
       proxy_http_version 1.1;
       # 禁用端口重定向
       port_in_redirect off;
       # 将所有请求代理到后端服务
       proxy_pass http://ai-base-home-tools.${NAMESPACES}.svc.cluster.local:3000;
    }
   
# 错误记录
  error_page 500 502 503 504 /50x.html;
    location = /50x.html {
       root /usr/share/nginx/html;
  }
  error_log    /tmp/ai.debug.log debug;
}